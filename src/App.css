/* General Container Styles */
.container {
  max-width: 900px;
  margin: 0 auto;
}

.card {
  margin-bottom: 20px;
}

.menu-cart {
  display: flex;
  justify-content: space-between;
}

.menu {
  flex: 1;
  margin-right: 20px;
  margin-bottom: 40px; 
}

.cart {
  flex: 1;
  margin-bottom: 40px; 
}

/* Toast Notification */
.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #28a745;
  color: white;
  padding: 10px 20px;
  border-radius: 5px;
  transition: opacity 0.5s ease;
}

.toast.hidden {
  opacity: 0;
}

* {
  box-sizing: border-box;
}

/* Body and Layout */
body {
  font-family: 'Raleway', sans-serif;
  margin: 0;
  background-color: #f8f8f8;
}

.app-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.app-title {
  text-align: center;
  margin-bottom: 20px;
  font-size: 36px;
  color: #333;
}

.content-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 20px;
}

.menu-section,
.cart-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  flex: 1;
  margin: 0 10px;
}

.outlet-name {
  font-size: 24px;
  margin-bottom: 10px;
}

.operational-time {
  font-size: 14px;
  color: #666;
}

@media (max-width: 768px) {
  .content-container {
    flex-direction: column;
  }

  .menu-section,
  .cart-section {
    margin: 10px 0;
  }
}

/* Outlet Selector */
.outlet-selector {
  margin-bottom: 40px;
  text-align: center;
}

.outlet-title {
  font-size: 24px;
  margin-bottom: 20px;
}

.outlet-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}

.outlet-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin: 10px;
  width: 300px;
  transition: transform 0.2s;
}

.outlet-card:hover {
  transform: scale(1.05);
}

.outlet-logo {
  margin-bottom: 10px;
  margin-top: 10px;
  width: 150px;
  height: 100px;
  object-fit: contain;
}

.outlet-info {
  padding: 15px;
}

.outlet-name {
  font-size: 20px;
  margin-bottom: 5px;
}

.outlet-time {
  font-size: 14px;
  color: #666;
}

/* Select Button */
.select-button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 10px 15px;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.select-button:hover {
  background-color: #0056b3;
}

/* Cart */
.cart-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.cart-title {
  font-size: 24px;
  margin-bottom: 15px;
}

.cart-summary {
  font-size: 16px;
  margin-bottom: 10px;
}

.cart-list {
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 15px;
}

.total-price {
  font-size: 20px;
  font-weight: bold;
}

.btn-danger {
  margin-left: 10px;
}

/* Menu */
.menu-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.menu-title {
  font-size: 24px;
  margin-bottom: 15px;
}

.menu-list {
  margin-bottom: 15px;
}

.menu-item-info {
  flex-grow: 1;
}

.quantity-controls {
  display: flex;
  align-items: center;
}

.quantity-controls button {
  margin: 0 5px;
}

.btn-block {
  width: 100%;
}

/* Thank You Page */
.thank-you-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #f0f4f8;
  text-align: center;
}

.thank-you-container h1 {
  font-size: 48px;
  color: #28a745;
  margin-bottom: 20px;
}

.thank-you-container p {
  font-size: 18px;
  color: #555;
  margin-bottom: 40px;
}

.thank-you-container button {
  background-color: #007bff;
  color: white;
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
}

.thank-you-container button:hover {
  background-color: #0056b3;
}
